import React, { useState, useEffect } from "react";
import {
  <PERSON>ton,
  <PERSON><PERSON>,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  Grid,
  MenuItem,
  TextField,
  Autocomplete,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  IconButton,
  InputAdornment,
} from "@mui/material";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import { FaPlus, FaEdit } from "react-icons/fa";
import api from "../../config/api";
import { useUser } from "../../context/UserContext";
import { NumericFormat } from 'react-number-format';

const RetireeDialog = ({
  row,
  schema,
  endpoint,
  dataListName,
  buttonProps = {},
}) => {
  const { currentUser } = useUser();
  const queryClient = useQueryClient();
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState({});
  const [errors, setErrors] = useState({});
  const isEditing = Boolean(row);

  // Fetch active fiscal year and settings
  const { data: settingsData } = useQuery({
    queryKey: ["settings", "active"],
    queryFn: async () => {
      const response = await api.get("/settings/active");
      return response.data;
    },
    enabled: open,
  });

  // Fetch employees eligible for retirement
  const { data: eligibleEmployees, isLoading: isLoadingEmployees } = useQuery({
    queryKey: ["eligible-for-retirement"],
    queryFn: async () => {
      const response = await api.get("/eligible-for-retirement");
      console.log("Eligible employees for retirement:", response.data);
      return response.data;
    },
    enabled: open && !isEditing,
  });

  useEffect(() => {
    if (open) {
      if (isEditing) {
        setFormData({
          ...row,
          processBy: `${currentUser?.FirstName || ""} ${
            currentUser?.LastName || ""
          }`,
          processDate: new Date().toISOString().split("T")[0],
        });
      } else {
        setFormData({
          employeeNumber: "",
          employeeFullName: "",
          positionTitle: "",
          department: "",
          division: "",
          region: "",
          retirementType: "Compulsory",
          dateOfRetirement: "",
          terminalLeave: 0,
          retirementGratuity: 0,
          total: 0,
          fiscalYear: settingsData?.fiscalYear || "",
          budgetType: settingsData?.budgetType || "",
          processBy: `${currentUser?.FirstName || ""} ${
            currentUser?.LastName || ""
          }`,
          processDate: new Date().toISOString().split("T")[0],
        });
      }
    }
  }, [open, row, isEditing, currentUser, settingsData]);

  // Calculate total when terminalLeave or retirementGratuity changes
  useEffect(() => {
    if (formData.terminalLeave !== undefined || formData.retirementGratuity !== undefined) {
      const terminalLeave = Number(formData.terminalLeave) || 0;
      const retirementGratuity = Number(formData.retirementGratuity) || 0;
      setFormData(prev => ({
        ...prev,
        total: terminalLeave + retirementGratuity
      }));
    }
  }, [formData.terminalLeave, formData.retirementGratuity]);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setFormData({});
    setErrors({});
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    
    // For numeric fields, ensure we store the actual number value
    if (name === "terminalLeave" || name === "retirementGratuity") {
      // Store as numeric value for calculations
      setFormData((prev) => ({
        ...prev,
        [name]: value,
        // Recalculate total whenever these values change
        total: Number(name === "terminalLeave" ? value : prev.terminalLeave || 0) + 
               Number(name === "retirementGratuity" ? value : prev.retirementGratuity || 0)
      }));
    } else {
      // For other fields, just update normally
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleEmployeeSelect = (event, employee) => {
    if (employee) {
      console.log("Selected employee:", employee);
      
      // Debug all possible employee number fields
      console.log("Possible employee number fields:", {
        employeeNumber: employee.employeeNumber,
        employeeID: employee.employeeID,
        EmployeeID: employee.EmployeeID,
        _id: employee._id
      });
      
      // Try to get employee number from any available field
      const employeeNum = employee.employeeNumber || 
                          employee.employeeID || 
                          employee.EmployeeID || 
                          (typeof employee._id === 'string' ? employee._id : "");
      
      console.log("Setting employee number:", employeeNum);
      
      setFormData((prev) => ({
        ...prev,
        employeeNumber: employeeNum,
        employeeFullName: employee.employeeFullName || employee.EmployeeFullName || "",
        positionTitle: employee.positionTitle || employee.PositionTitle || "",
        department: employee.department || employee.Department || "",
        division: employee.division || employee.Division || "",
        region: employee.region || employee.Region || "",
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    // Required fields validation
    if (!formData.employeeNumber) {
      newErrors.employeeNumber = "Employee number is required";
    }
    
    if (!formData.employeeFullName) {
      newErrors.employeeFullName = "Employee name is required";
    }
    
    if (!formData.dateOfRetirement) {
      newErrors.dateOfRetirement = "Retirement date is required";
    }
    
    if (!formData.retirementType) {
      newErrors.retirementType = "Retirement type is required";
    }
    
    // Add validation for amounts
    if (!formData.terminalLeave || Number(formData.terminalLeave) <= 0) {
      newErrors.terminalLeave = "Terminal leave amount is required";
    }
    
    if (!formData.retirementGratuity || Number(formData.retirementGratuity) <= 0) {
      newErrors.retirementGratuity = "Retirement gratuity amount is required";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Create mutation
  const createMutation = useMutation({
    mutationFn: async (data) => {
      console.log("Sending data to server:", data);
      const response = await api.post(endpoint, data);
      console.log("Server response:", response.data);
      return response;
    },
    onSuccess: (response) => {
      toast.success("Retiree record created successfully");
      console.log("Successfully created retiree:", response.data);
      // Invalidate queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ["retiree"] });
      handleClose();
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Error creating record");
      console.error("Error creating retiree record:", error);
    },
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: async (data) => {
      return await api.put(`${endpoint}/${row._id}`, data);
    },
    onSuccess: () => {
      toast.success("Retiree record updated successfully");
      // Make sure we're invalidating the correct query
      queryClient.invalidateQueries({ queryKey: [dataListName] });
      // Also invalidate any related queries
      queryClient.invalidateQueries({ queryKey: ["retiree"] });
      handleClose();
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Error updating record");
      console.error("Error updating retiree record:", error);
    },
  });

  // Fix the handleSubmit function to properly format the data
  const handleSubmit = () => {
    if (!validateForm()) {
      console.log("Form validation failed. Errors:", errors);
      return;
    }
    
    // Prepare the data for submission
    const submissionData = {
      ...formData,
      // Ensure numeric fields are sent as numbers
      terminalLeave: parseFloat(formData.terminalLeave) || 0,
      retirementGratuity: parseFloat(formData.retirementGratuity) || 0,
      total: parseFloat(formData.terminalLeave || 0) + parseFloat(formData.retirementGratuity || 0),
      // Add process information
      processBy: "Current User", // Replace with actual user info
      processDate: new Date().toISOString(),
      // Add default values if not provided
      fiscalYear: formData.fiscalYear || new Date().getFullYear().toString(),
      budgetType: formData.budgetType || "Initial"
    };
    
    console.log("Submitting form data:", submissionData);
    
    if (isEditing) {
      updateMutation.mutate(submissionData);
    } else {
      createMutation.mutate(submissionData);
    }
  };

  // Add a useEffect to log form data changes
  useEffect(() => {
    console.log("Current form data:", formData);
  }, [formData]);

  // Custom component for currency formatting
  const NumberFormatCustom = React.forwardRef(function NumberFormatCustom(props, ref) {
    const { onChange, ...other } = props;
    return (
      <NumericFormat
        {...other}
        getInputRef={ref}
        thousandSeparator
        decimalScale={2}
        fixedDecimalScale
        prefix="₱ "
        onValueChange={(values) => {
          onChange({
            target: {
              name: props.name,
              value: values.value,
            },
          });
        }}
      />
    );
  });

  return (
    <>
      {isEditing ? (
        <IconButton onClick={handleOpen} color="primary" size="small">
          <FaEdit />
        </IconButton>
      ) : (
        <Button
          variant="contained"
          startIcon={<FaPlus />}
          onClick={handleOpen}
          {...buttonProps}
        >
          Add Retiree
        </Button>
      )}

      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          {isEditing ? "Edit Retiree Record" : "Add New Retiree Record"}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            {!isEditing && (
              <Grid item xs={12}>
                <Autocomplete
                  options={Array.isArray(eligibleEmployees) ? eligibleEmployees : []}
                  getOptionLabel={(option) => 
                    `${option.employeeNumber || ''} - ${option.employeeFullName || ''} (${option.retirementType || ''})`
                  }
                  onChange={handleEmployeeSelect}
                  loading={isLoadingEmployees}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Select Employee Eligible for Retirement"
                      fullWidth
                      error={!!errors.employeeFullName}
                      helperText={errors.employeeFullName}
                    />
                  )}
                />
              </Grid>
            )}

            {/* Hidden fields - we keep them in the form but don't display them */}
            <input type="hidden" name="employeeNumber" value={formData.employeeNumber || ""} />
            <input type="hidden" name="employeeFullName" value={formData.employeeFullName || ""} />
            <input type="hidden" name="fiscalYear" value={formData.fiscalYear || ""} />
            <input type="hidden" name="budgetType" value={formData.budgetType || ""} />

            <Grid item xs={12} md={6}>
              <TextField
                name="positionTitle"
                label="Position Title"
                value={formData.positionTitle || ""}
                onChange={handleChange}
                fullWidth
                disabled={true}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                name="department"
                label="Department"
                value={formData.department || ""}
                onChange={handleChange}
                fullWidth
                disabled={true}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                name="division"
                label="Division"
                value={formData.division || ""}
                onChange={handleChange}
                fullWidth
                disabled={true}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                name="region"
                label="Region"
                value={formData.region || ""}
                onChange={handleChange}
                fullWidth
                disabled={true}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth required error={!!errors.retirementType}>
                <InputLabel>Retirement Type</InputLabel>
                <Select
                  name="retirementType"
                  value={formData.retirementType || ""}
                  onChange={handleChange}
                  label="Retirement Type"
                  disabled={isEditing}
                >
                  <MenuItem value="Compulsory">Compulsory</MenuItem>
                  <MenuItem value="Optional">Optional</MenuItem>
                  <MenuItem value="Disability">Disability</MenuItem>
                </Select>
                {errors.retirementType && (
                  <FormHelperText>{errors.retirementType}</FormHelperText>
                )}
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                name="dateOfRetirement"
                label="Date of Retirement"
                type="date"
                value={formData.dateOfRetirement || ""}
                onChange={handleChange}
                fullWidth
                required
                error={!!errors.dateOfRetirement}
                helperText={errors.dateOfRetirement}
                InputLabelProps={{ shrink: true }}
                // Prevent selecting dates in the past
                inputProps={{ 
                  min: new Date().toISOString().split('T')[0]
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                name="terminalLeave"
                label="Terminal Leave Amount"
                value={formData.terminalLeave || ""}
                onChange={handleChange}
                fullWidth
                required
                error={!!errors.terminalLeave}
                helperText={errors.terminalLeave}
                InputProps={{
                  inputComponent: NumberFormatCustom,
                  inputProps: { style: { textAlign: 'right' } }
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                name="retirementGratuity"
                label="Retirement Gratuity Amount"
                value={formData.retirementGratuity || ""}
                onChange={handleChange}
                fullWidth
                required
                error={!!errors.retirementGratuity}
                helperText={errors.retirementGratuity}
                InputProps={{
                  inputComponent: NumberFormatCustom,
                  inputProps: { style: { textAlign: 'right' } }
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                name="total"
                label="Total Amount"
                value={
                  (Number(formData.terminalLeave || 0) + Number(formData.retirementGratuity || 0))
                    .toLocaleString('en-PH', { style: 'currency', currency: 'PHP' })
                    .replace('PHP', '₱')
                }
                fullWidth
                disabled
                InputProps={{
                  readOnly: true,
                  style: { fontWeight: 'bold' }
                }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button 
            variant="contained" 
            color="primary" 
            onClick={handleSubmit}
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {isEditing ? "Update" : "Save"}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default RetireeDialog;















