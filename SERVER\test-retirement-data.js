const mongoose = require('mongoose');
const Employee = require('./models/EmployeeList');

// Connect to MongoDB using the same connection string as the main app
mongoose.connect('*************************************************************************');

async function createTestEmployees() {
  try {
    console.log('Creating test employees for retirement eligibility...');

    // Test employee 1: Eligible for compulsory retirement (67 years old)
    const compulsoryEmployee = new Employee({
      Department: "TEST DEPARTMENT",
      PositionTitle: "Test Position - Compulsory",
      Division: "TEST DIVISION",
      Region: "Central Office",
      StatusOfAppointment: "PERMANENT",
      SG: 15,
      Step: 1,
      JG: "10",
      Rate: 36619,
      EmployeeID: "TEST001",
      EmployeeFullName: "Test Employee Compulsory Retirement",
      DateOfAppointment: new Date("1980-01-01"), // 45 years of service
      DateOfBirth: new Date("1958-01-01"), // 67 years old
      employeeStatus: "Active"
    });

    // Test employee 2: Eligible for optional retirement (62 years old, 20 years service)
    const optionalEmployee = new Employee({
      Department: "TEST DEPARTMENT",
      PositionTitle: "Test Position - Optional",
      Division: "TEST DIVISION",
      Region: "Central Office",
      StatusOfAppointment: "PERMANENT",
      SG: 18,
      Step: 3,
      JG: "11",
      Rate: 45000,
      EmployeeID: "TEST002",
      EmployeeFullName: "Test Employee Optional Retirement",
      DateOfAppointment: new Date("2005-01-01"), // 20 years of service
      DateOfBirth: new Date("1963-01-01"), // 62 years old
      employeeStatus: "Active"
    });

    // Test employee 3: Not eligible (60 years old but only 10 years service)
    const notEligibleEmployee = new Employee({
      Department: "TEST DEPARTMENT",
      PositionTitle: "Test Position - Not Eligible",
      Division: "TEST DIVISION",
      Region: "Central Office",
      StatusOfAppointment: "PERMANENT",
      SG: 12,
      Step: 2,
      JG: "8",
      Rate: 28000,
      EmployeeID: "TEST003",
      EmployeeFullName: "Test Employee Not Eligible",
      DateOfAppointment: new Date("2015-01-01"), // 10 years of service
      DateOfBirth: new Date("1965-01-01"), // 60 years old
      employeeStatus: "Active"
    });

    // Save the test employees
    await compulsoryEmployee.save();
    console.log('Created compulsory retirement test employee');

    await optionalEmployee.save();
    console.log('Created optional retirement test employee');

    await notEligibleEmployee.save();
    console.log('Created not eligible test employee');

    console.log('Test employees created successfully!');
    
    // Test the retirement eligibility
    console.log('\nTesting retirement eligibility...');
    const currentDate = new Date();
    
    const testEmployees = [compulsoryEmployee, optionalEmployee, notEligibleEmployee];
    
    for (const employee of testEmployees) {
      // Calculate age more accurately
      const birthDate = new Date(employee.DateOfBirth);
      let age = currentDate.getFullYear() - birthDate.getFullYear();
      const monthDiff = currentDate.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && currentDate.getDate() < birthDate.getDate())) {
        age--;
      }
      
      // Calculate years of service more accurately
      const appointmentDate = new Date(employee.DateOfAppointment);
      let yearsOfService = currentDate.getFullYear() - appointmentDate.getFullYear();
      const serviceMonthDiff = currentDate.getMonth() - appointmentDate.getMonth();
      if (serviceMonthDiff < 0 || (serviceMonthDiff === 0 && currentDate.getDate() < appointmentDate.getDate())) {
        yearsOfService--;
      }
      
      // Check eligibility
      const isEligibleForCompulsory = age >= 65;
      const isEligibleForOptional = (age >= 60 && age < 65) && yearsOfService >= 15;
      
      console.log(`\n${employee.EmployeeFullName}:`);
      console.log(`  Age: ${age}`);
      console.log(`  Years of Service: ${yearsOfService}`);
      console.log(`  Eligible for Compulsory: ${isEligibleForCompulsory}`);
      console.log(`  Eligible for Optional: ${isEligibleForOptional}`);
      console.log(`  Overall Eligible: ${isEligibleForCompulsory || isEligibleForOptional}`);
    }

  } catch (error) {
    console.error('Error creating test employees:', error);
  } finally {
    mongoose.connection.close();
  }
}

createTestEmployees();
